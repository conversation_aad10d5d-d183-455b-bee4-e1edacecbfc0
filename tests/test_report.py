import unittest
import sys
import os
import json
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.model_service import ModelService
from services.document_parser import DocumentParser
from services.report_analyzer import ReportAnalyzer
from dotenv import load_dotenv

load_dotenv()
model_service = ModelService()

def test_analyze_section():
        # 准备测试数据
        project_name = "广西电网项目"
        section_title = "测试章节"
        section_content = "这是一个测试章节内容，包含一些测试文本用于分析。"
        all_criteria = [
            {
                "id": "CR001",
                "content": "审查细则1: 项目名称应明确具体"
            },
            {
                "id": "CR002",
                "content": "审查细则2: 应包含详细的实施方案"
            }
        ]
        review_guide = "审查指南：请确保项目名称明确具体，并包含详细的实施方案。"

        # 调用被测方法
        result = model_service.analyze_section_batch(
            project_name,
            section_title,
            section_content,
            all_criteria,
            review_guide
        )

        # 验证返回结果是否为JSON格式
        try:
            print(f"审查结果: {result['criteria_results']}")
        except Exception as e:
            raise ValueError(f"返回结果不是有效的JSON格式: {e}")

def test_analyze_single_section(pdf_path, section_title):
        # 初始化服务
        model_service = ModelService()
        document_parser = DocumentParser()
        report_analyzer = ReportAnalyzer(model_service, document_parser)

        # 加载模板文件
        report_analyzer._load_templates()
        print(f"审查细则总数: {len(report_analyzer.criteria)}")

        # 调用被测方法
        section_content,result = report_analyzer.analyze_single_section(pdf_path, section_title)

        print(f"返回结果类型: {type(result)}")
        print(f"返回结果: {result}")

        # 验证返回结果是否为JSON格式
        try:
            # 验证是否包含预期的键
            print(f"！！！审查结果: {result['criteria_results']}")
            print(f"实际返回结果数量: {len(result['criteria_results'])}")
            print(f"期望结果数量: {len(report_analyzer.criteria)}")

            # 检查是否有错误结果
            for i, criterion_result in enumerate(result['criteria_results']):
                criterion_id = criterion_result.get('criterion_id', 'unknown')
                if criterion_id in ['api_error', 'parse_error']:
                    print(f"发现错误结果 {i}: {criterion_result}")

        except Exception as e:
            raise ValueError(f"解析错误: {e}")
def test_analyze_pdf(pdf_path):
        # 初始化服务
        model_service = ModelService()
        document_parser = DocumentParser()
        report_analyzer = ReportAnalyzer(model_service, document_parser)

        # 加载模板文件
        report_analyzer._load_templates()
        print(f"审查细则总数: {len(report_analyzer.criteria)}")

        # 调用被测方法
        result = report_analyzer.analyze(pdf_path)

        print(f"返回结果类型: {type(result)}")
        # 将reult写入当前目录的0report_result.json文件
        with open('0report_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=4)
        #print(f"返回结果: {result}")


if __name__ == '__main__':
    # test_analyze_single_section("templates/test_可行性研究报告.pdf", "1 概述")
    test_analyze_pdf("templates/test_可行性研究报告.pdf")