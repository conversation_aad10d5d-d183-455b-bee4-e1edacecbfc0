import os
import re
import time
from openai import OpenAI
from typing import Dict, Any

nothink = " "
class ModelService:
    def __init__(self):
        # 在测试模式下不需要真实的API客户端
        if os.getenv("TEST_MODE") == "true":
            self.client = None
        else:
            self.client = OpenAI(
                api_key=os.getenv("OPENAI_API_KEY"),
                base_url=os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
            )

        # 初始化统计信息
        self.reset_stats()

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_tokens": 0,
            "api_calls": 0,
            "total_api_time": 0.0
        }

    def get_stats(self):
        """获取统计信息"""
        return self.stats.copy()

    def _get_system_prompt(self, chapter_outline: str, criteria_text: str, review_guide: str = None) -> str:
        return f"""
# 角色
你是专业的可研报告评审专家，可以根据《审查指南》（见下文）及《审查细则》（见下文）对用户提供的章节内容进行评审。

# 职责
对提供的章节内容，参考《可研报告编制大纲》中该章节的要求，逐一检查用户提供的可研报告章节内容对所有审查细则的符合情况，并输出审查结果：（符合、基本符合、不符合），不符合情况给出具体原因。并输出结构化的JSON格式评审结果。

# 工作流程
1. 仔细阅读章节内容，100字总结该章节的内容，做为输出格式定义中"summary"字段的值。
2. 根据编制大纲中该章节的的内容和可研报告中该章节内容，逐一检查每个审查细则与该章节的相关性：
  - 2.1 如果不相关可输出:不适用;
  - 2.2 如果相关则需要根据审查细则进行符合性评审, 给出审查情况。
  - 2.3 审查时需要根据编制大纲中该章节要求对可研报告该章节内容进行评审。
  - 2.4 审查时需要参考《审查指南》的要求对可研报告该章节内容评审。
3. 示例：以第1章节为例，对于"审查细则1"可能输出内容包括：
  - 当章节1与"审查项1"不相关时，可输出：不适用该审查项
  - 当章节1与"审查项1"相关时，可输出：具体的审查情况，如
    * 或 章节1 基本符合
    * 或 章节1 改动了大纲标题：1.2
    * 或 章节1 投资估算编制说明应包括工程量确定的主要依据和计算原则

# 评审标准
- **符合**：章节内容完全满足审查细则要求
- **基本符合**：章节内容大部分满足要求，但有轻微不足
- **不符合**：章节内容明显不满足审查细则要求
- **不适用**：该审查细则与当前章节内容无关

# 输出格式
请严格按照以下JSON格式输出，不要添加任何其他文字：
```json
{{
  "summary": "100字以内总结该章节原文本内容",
  "criteria_results": [
    {{
      "criterion_id": "审查细则编号",
      "result": "符合/基本符合/不符合/不适用",
      "explanation": "具体说明原因，如果不符合请详细说明问题"
    }}
  ]
}}
```

# 注意事项
1. 必须对每个审查细则都给出评审结果
2. 评审要基于章节实际内容，不要主观臆测
3. 不符合的情况要具体说明问题所在
4. 不适用的情况要说明为什么与该章节无关
5. 输出必须是有效的JSON格式

# 当前章节编制大纲要求：
{chapter_outline if chapter_outline else '未提供该章节的大纲信息'}

# 审查指南：
{review_guide if review_guide else '未提供审查指南'}

# 所有审查细则：
{criteria_text}
        """
    def _clean_response(self, content: str) -> str:
        """清理推理模型的响应内容，去除思考标签"""
        # 去除 <think>...</think> 标签及其内容
        content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)

        # 去除 <thinking>...</thinking> 标签及其内容
        content = re.sub(r'<thinking>.*?</thinking>', '', content, flags=re.DOTALL)

        # 去除多余的空行
        content = re.sub(r'\n\s*\n', '\n\n', content)

        return content.strip()

    def analyze_section_batch(self, project_name: str, section_title: str, section_content: str, all_criteria: list, chapter_outline: str = None, review_guide: str = None, debug_callback=None) -> Dict[str, Any]:
        """批量分析单个章节对所有审查细则的符合情况"""

        # 调试信息回调函数
        def debug_log(message, level="info"):
            if debug_callback:
                debug_callback(message, level)
            print(f"[{level.upper()}] {message}")

        debug_log(f"开始分析章节: {section_title}")
        debug_log(f"章节内容长度: {len(section_content)} 字符")

        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            debug_log("运行在测试模式", "warning")

            # 模拟API调用统计信息
            mock_api_time = 0.5  # 模拟0.5秒的API调用时间
            mock_input_tokens = 1000  # 模拟输入token数
            mock_output_tokens = 200  # 模拟输出token数

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += mock_api_time
            self.stats["total_input_tokens"] += mock_input_tokens
            self.stats["total_output_tokens"] += mock_output_tokens
            self.stats["total_tokens"] += mock_input_tokens + mock_output_tokens

            debug_log(f"模拟API调用，耗时: {mock_api_time}秒, 输入Token: {mock_input_tokens}, 输出Token: {mock_output_tokens}", "success")

            mock_results = []
            for i, criterion in enumerate(all_criteria):
                mock_results.append({
                    "criterion_id": criterion.get("id", f"test_{i}"),
                    "result": "基本符合",
                    "explanation": f"[测试模式] 章节 {section_title} 对审查细则 {criterion.get('id', i)} 的评审结果"
                })
            debug_log(f"生成 {len(mock_results)} 个模拟评审结果", "success")
            return {"criteria_results": mock_results}
        # 如果章节内容为空，直接返回
        if not section_content.strip():
            #debug_log(f"章节 {section_title} 内容为空，跳过分析", "warning")
            empty_results = []
            for i, criterion in enumerate(all_criteria):
                empty_results.append({
                    "criterion_id": criterion.get("id", f"test_{i}"),
                    "result": "不符合",
                    "explanation": f" 章节 {section_title} 内容为空"
                })
            debug_log(f"为空章节生成 {len(empty_results)} 个不符合结果", "info")
            return {"criteria_results": empty_results}

        # 构建所有审查细则的文本，过滤无效数据
        criteria_text = ""
        valid_criteria_count = 0

        for i, criterion in enumerate(all_criteria, 1):
            criterion_id = str(criterion.get('id', '')).strip()
            criterion_content = str(criterion.get('content', '')).strip()
            criterion_requirements = str(criterion.get('requirements', '')).strip()

            # 跳过空的或无效的审查细则
            if (not criterion_content or
                criterion_content in ['未指定', '未知内容', 'nan', 'NaN', '']):
                print(f"跳过无效的审查细则 {i}: ID='{criterion_id}', 内容='{criterion_content[:30]}...'")
                continue

            valid_criteria_count += 1
            criteria_text += f"""
审查细则 {valid_criteria_count}：
- 编号：{criterion_id}
- 审查范畴：{criterion_requirements if criterion_requirements and criterion_requirements != 'nan' else '未指定'}
- 审查内容：{criterion_content}
"""

        debug_log(f"有效审查细则数量: {valid_criteria_count} / {len(all_criteria)}")

        try:
            debug_log(f"准备调用大模型分析章节: {section_title}")
            # debug_log(f"   -----审查指南(长度={len(review_guide) if review_guide else 0} 字符) {review_guide[:50] if review_guide else ''}")
            debug_log(f"   -----该章节的编制大纲(长度={len(chapter_outline) if chapter_outline else 0} 字符) {chapter_outline[:50] if chapter_outline else ''}")
            debug_log(f"   -----该章节的报告内容(长度={len(section_content) if section_content else 0} 字符) {section_content[:50]}")

            sys_prompt = self._get_system_prompt(chapter_outline, criteria_text, review_guide)
            user_content = f"""
请对以下章节内容进行全面的合规性审查：
可研报告项目名称: {project_name}

章节标题：{section_title}

章节内容：
{section_content if section_content.strip() else '该章节内容为空'}

请按照系统提示中的JSON格式，对该章节逐一评审所有审查细则的符合情况。
"""

            debug_log(f"\n\n正在调用大模型API...", "info")
            # debug_log(f"           ------------系统提示词:{sys_prompt}", "info")
            # debug_log(f"           ------------用户请求:{user_content}", "info")

            # 记录API调用开始时间
            api_start_time = time.time()

            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": sys_prompt},
                    {"role": "user", "content": f"{user_content} {nothink}"}
                ],
                timeout=120  # 设置120秒超时
            )

            # 记录API调用结束时间
            api_end_time = time.time()
            api_duration = api_end_time - api_start_time

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += api_duration

            # 提取token使用信息
            if hasattr(response, 'usage') and response.usage:
                if hasattr(response.usage, 'prompt_tokens'):
                    self.stats["total_input_tokens"] += response.usage.prompt_tokens
                if hasattr(response.usage, 'completion_tokens'):
                    self.stats["total_output_tokens"] += response.usage.completion_tokens
                if hasattr(response.usage, 'total_tokens'):
                    self.stats["total_tokens"] += response.usage.total_tokens

                debug_log(f"API调用成功，耗时: {api_duration:.2f}秒, 输入Token: {response.usage.prompt_tokens}, 输出Token: {response.usage.completion_tokens}", "success")
            else:
                debug_log(f"API调用成功，耗时: {api_duration:.2f}秒", "success")

            # 检查响应结构
            if not response:
                raise ValueError("API响应为空")

            if not hasattr(response, 'choices') or not response.choices:
                debug_log(f"API响应结构异常: {response}", "error")
                raise ValueError("API响应中没有choices字段或choices为空")

            if len(response.choices) == 0:
                raise ValueError("API响应choices数组为空")

            if not response.choices[0]:
                raise ValueError("API响应choices[0]为空")

            if not hasattr(response.choices[0], 'message') or not response.choices[0].message:
                raise ValueError("API响应choices[0]没有message字段或message为空")

            if not hasattr(response.choices[0].message, 'content'):
                raise ValueError("API响应choices[0].message没有content字段")

            # 清理响应内容
            raw_content = response.choices[0].message.content
            if raw_content is None:
                raise ValueError("API响应content为None")

            cleaned_content = self._clean_response(raw_content)

            debug_log(f"收到大模型响应，长度: {len(raw_content)} 字符")
            debug_log(f"清理后响应长度: {len(cleaned_content)} 字符")
            debug_log(f"审查结果的响应预览: {cleaned_content[:100]}...")

            # 尝试解析JSON响应
            try:
                import json
                # 提取JSON部分
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)

                    # 统计评审结果
                    if "criteria_results" in result:
                        criteria_count = len(result["criteria_results"])
                        result_stats = {}
                        for criterion in result["criteria_results"]:
                            result_type = criterion.get("result", "未知")
                            result_stats[result_type] = result_stats.get(result_type, 0) + 1

                        debug_log(f"解析成功，获得 {criteria_count} 个评审结果", "success")
                        debug_log(f"结果统计: {result_stats}")

                    return result
                else:
                    raise ValueError("未找到有效的JSON格式")
            except Exception as json_error:
                debug_log(f"JSON解析失败: {json_error}", "error")
                debug_log(f"原始响应: {cleaned_content[:500]}...", "error")
                # 如果JSON解析失败，返回原始文本格式
                return {
                    "criteria_results": [{
                        "criterion_id": "parse_error",
                        "result": "不适用",
                        "explanation": f"大模型响应格式错误，原始响应：{cleaned_content[:500]}"
                    }]
                }

        except Exception as e:
            debug_log(f"API调用失败: {e}", "error")
            return {
                "criteria_results": [{
                    "criterion_id": "api_error",
                    "criterion_content": "API调用失败",
                    "result": "不适用",
                    "explanation": f"API调用失败：{str(e)}"
                }]
            }

    def _prepare_report_summary(self, all_sections: dict) -> str:
        """准备报告全文摘要"""
        summary_parts = []
        for section_title, section_content in all_sections.items():
            if section_content and section_content.strip():
                # 截取每个章节的前500字符作为摘要
                content_preview = section_content.strip()[:500]
                summary_parts.append(f"【{section_title}】: {content_preview}...")
            else:
                summary_parts.append(f"【{section_title}】: (章节内容为空)")

        return "\n\n".join(summary_parts)

    def analyze_criteria_comprehensive(self, review_guide:str, criterion: dict, report_summary: str) -> Dict[str, Any]:
        """对单个审查细则进行全文综合分析"""
        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            criterion_id = criterion.get("criterion_id", "unknown")
            return {
                "comprehensive_analysis": f"[测试模式] 对审查细则 {criterion_id} 的全文综合分析",
                "overall_assessment": "基本符合",
                "key_findings": [f"[测试模式] 审查细则 {criterion_id} 的关键发现"],
                "recommendations": [f"[测试模式] 审查细则 {criterion_id} 的改进建议"]
            }

        criterion_id = criterion.get("criterion_id", "unknown")
        criterion_content = criterion.get("criterion_content", "")
        section_results = criterion.get("section_results", [])

        # 构建章节分析摘要
        section_analysis_summary = []
        for result in section_results:
            section_analysis_summary.append(
                f"章节【{result['section']}】: {result['result']} - {result['explanation']}"
            )

        try:
            # 构建系统提示词，避免f-string中的格式化问题
            system_prompt = """
# 角色
你是专业的可研报告评审专家，负责对审查细则进行全文综合分析。

# 任务
基于审查指南和各章节的初步分析结果，对单个审查细则给出全文综合评审意见。

# 分析要求
1. 综合考虑报告全文内容，不仅仅是单个章节
2. 基于各章节的分析结果，给出该审查细则的整体符合情况
3. 识别关键发现和问题
4. 提供具体的改进建议

# 输出格式
请严格按照以下JSON格式输出：
```json
{
  "comprehensive_analysis": "对该审查细则的全文综合分析，包括整体评价和关键发现",
  "overall_assessment": "符合/基本符合/不符合/不适用",
  "key_findings": [
    "关键发现1",
    "关键发现2"
  ],
  "recommendations": [
    "具体改进建议1",
    "具体改进建议2"
  ]
}
```

# 评审标准
- **符合**：报告全文完全满足该审查细则要求
- **基本符合**：报告全文大部分满足要求，但有轻微不足
- **不符合**：报告全文明显不满足该审查细则要求
- **不适用**：该审查细则与报告内容无关

# 审查指南：
""" + (review_guide if review_guide else '未提供审查指南')

            user_content = f"""
请对以下审查细则进行全文综合分析：

审查细则ID：{criterion_id}
审查细则内容：{criterion_content}

各章节分析结果：
{chr(10).join(section_analysis_summary) if section_analysis_summary else '暂无章节分析结果'}

请基于以上信息，给出该审查细则的全文综合评审意见。
"""
            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"{user_content} {nothink}"}
                ],
                timeout=120
            )

            # 清理响应内容
            raw_content = response.choices[0].message.content
            cleaned_content = self._clean_response(raw_content)

            # 解析JSON响应
            try:
                import json
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)
                    return result
                else:
                    raise ValueError("未找到有效的JSON格式")
            except Exception as json_error:
                print(f"JSON解析失败: {json_error}")
                return {
                    "comprehensive_analysis": f"综合分析失败，原始响应：{cleaned_content[:200]}...",
                    "overall_assessment": "不适用",
                    "key_findings": ["分析过程出现错误"],
                    "recommendations": ["建议重新进行分析"]
                }

        except Exception as e:
            print(f"综合分析API调用失败: {e}")
            return {
                "comprehensive_analysis": f"API调用失败：{str(e)}",
                "overall_assessment": "不适用",
                "key_findings": ["API调用失败"],
                "recommendations": ["请检查网络连接和API配置"]
            }

    def analyze_criteria_comprehensive_batch(self, review_guide: str, criteria_results: list) -> list:
        """批量对所有审查细则进行全文综合分析"""
        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            # 模拟API调用统计信息
            mock_api_time = 1.0  # 批量处理模拟更长时间
            mock_input_tokens = 2000  # 批量处理模拟更多token
            mock_output_tokens = 500

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += mock_api_time
            self.stats["total_input_tokens"] += mock_input_tokens
            self.stats["total_output_tokens"] += mock_output_tokens
            self.stats["total_tokens"] += mock_input_tokens + mock_output_tokens

            print(f"[测试模式] 模拟批量综合分析API调用，耗时: {mock_api_time}秒, 输入Token: {mock_input_tokens}, 输出Token: {mock_output_tokens}")

            batch_results = []
            for criterion in criteria_results:
                criterion_id = criterion.get("criterion_id", "unknown")
                batch_results.append({
                    "comprehensive_analysis": f"[测试模式] 对审查细则 {criterion_id} 的批量全文综合分析",
                    "overall_assessment": "基本符合",
                    "key_findings": [f"[测试模式] 审查细则 {criterion_id} 的关键发现"],
                    "recommendations": [f"[测试模式] 审查细则 {criterion_id} 的改进建议"]
                })
            return batch_results

        try:
            # 构建所有审查细则的信息
            criteria_info = []
            for criterion in criteria_results:
                criterion_id = criterion.get("criterion_id", "unknown")
                criterion_content = criterion.get("criterion_content", "")
                section_results = criterion.get("section_results", [])

                # 构建章节分析摘要
                section_analysis_summary = []
                for result in section_results:
                    section_analysis_summary.append(
                        f"章节【{result['section']}】: {result['result']} - {result['explanation']}"
                    )

                criteria_info.append({
                    "criterion_id": criterion_id,
                    "criterion_content": criterion_content,
                    "section_analysis": "\n".join(section_analysis_summary) if section_analysis_summary else "暂无章节分析结果"
                })

            # 构建批量分析的系统提示词
            system_prompt = """
# 角色
你是专业的可研报告评审专家，负责对多个审查细则进行批量全文综合分析。

# 任务
基于审查指南和各章节的初步分析结果，对所有审查细则给出全文综合评审意见。

# 分析要求
1. 综合考虑报告全文内容，不仅仅是单个章节
2. 基于各章节的分析结果，给出每个审查细则的整体符合情况
3. 识别关键发现和问题
4. 提供具体的改进建议

# 输出格式
请严格按照以下JSON格式输出，包含所有审查细则的分析结果：
```json
{
  "criteria_comprehensive_results": [
    {
      "criterion_id": "审查细则ID",
      "comprehensive_analysis": "对该审查细则的全文综合分析，包括整体评价和关键发现",
      "overall_assessment": "符合/基本符合/不符合/不适用",
      "key_findings": [
        "关键发现1",
        "关键发现2"
      ],
      "recommendations": [
        "具体改进建议1",
        "具体改进建议2"
      ]
    }
  ]
}
```

# 评审标准
- **符合**：报告全文完全满足该审查细则要求
- **基本符合**：报告全文大部分满足要求，但有轻微不足
- **不符合**：报告全文明显不满足该审查细则要求
- **不适用**：该审查细则与报告内容无关

# 审查指南：
""" + (review_guide if review_guide else '未提供审查指南')

            # 构建用户提示词
            criteria_text = ""
            for i, criterion_info in enumerate(criteria_info, 1):
                criteria_text += f"""
## 审查细则 {i}
- **ID**: {criterion_info['criterion_id']}
- **内容**: {criterion_info['criterion_content']}
- **各章节分析结果**:
{criterion_info['section_analysis']}

"""

            user_prompt = f"""
请对以下所有审查细则进行批量全文综合分析：

{criteria_text}

请基于以上信息，给出每个审查细则的全文综合评审意见。
注意：必须按照JSON格式输出，并且结果数组的顺序要与输入的审查细则顺序一致。
"""

            print(f"正在调用大模型进行批量全文综合分析...")
            print(f"共处理 {len(criteria_results)} 个审查细则")

            # 记录API调用开始时间
            api_start_time = time.time()

            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"{user_prompt} {nothink}"}
                ],
                timeout=180  # 批量处理需要更长时间
            )

            # 记录API调用结束时间
            api_end_time = time.time()
            api_duration = api_end_time - api_start_time

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += api_duration

            # 提取token使用信息
            if hasattr(response, 'usage') and response.usage:
                if hasattr(response.usage, 'prompt_tokens'):
                    self.stats["total_input_tokens"] += response.usage.prompt_tokens
                if hasattr(response.usage, 'completion_tokens'):
                    self.stats["total_output_tokens"] += response.usage.completion_tokens
                if hasattr(response.usage, 'total_tokens'):
                    self.stats["total_tokens"] += response.usage.total_tokens

                print(f"批量综合分析API调用成功，耗时: {api_duration:.2f}秒, 输入Token: {response.usage.prompt_tokens}, 输出Token: {response.usage.completion_tokens}")
            else:
                print(f"批量综合分析API调用成功，耗时: {api_duration:.2f}秒")

            # 清理响应内容
            raw_content = response.choices[0].message.content
            cleaned_content = self._clean_response(raw_content)

            print(f"收到批量综合分析响应，长度: {len(raw_content)} 字符")

            # 解析JSON响应
            try:
                import json
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)

                    if "criteria_comprehensive_results" in result:
                        batch_results = result["criteria_comprehensive_results"]
                        print(f"成功解析批量综合分析结果，共 {len(batch_results)} 个结果")
                        return batch_results
                    else:
                        raise ValueError("响应中未找到criteria_comprehensive_results字段")
                else:
                    raise ValueError("未找到有效的JSON格式")
            except Exception as json_error:
                print(f"批量综合分析JSON解析失败: {json_error}")
                print(f"原始响应: {cleaned_content[:500]}...")

                # 如果解析失败，返回默认结果
                fallback_results = []
                for criterion in criteria_results:
                    criterion_id = criterion.get("criterion_id", "unknown")
                    fallback_results.append({
                        "comprehensive_analysis": f"批量分析解析失败，原始响应：{cleaned_content[:200]}...",
                        "overall_assessment": "不适用",
                        "key_findings": ["批量分析解析失败"],
                        "recommendations": ["建议重新进行分析"]
                    })
                return fallback_results

        except Exception as e:
            print(f"批量综合分析API调用失败: {e}")
            # 返回默认结果
            fallback_results = []
            for criterion in criteria_results:
                criterion_id = criterion.get("criterion_id", "unknown")
                fallback_results.append({
                    "comprehensive_analysis": f"批量API调用失败：{str(e)}",
                    "overall_assessment": "不适用",
                    "key_findings": ["批量API调用失败"],
                    "recommendations": ["请检查网络连接和API配置"]
                })
            return fallback_results

    def summarize_review(self, review_results: list) -> Dict[str, Any]:
        """汇总所有评审结果"""
        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            # 模拟API调用统计信息
            mock_api_time = 0.8
            mock_input_tokens = 1500
            mock_output_tokens = 300

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += mock_api_time
            self.stats["total_input_tokens"] += mock_input_tokens
            self.stats["total_output_tokens"] += mock_output_tokens
            self.stats["total_tokens"] += mock_input_tokens + mock_output_tokens

            print(f"[测试模式] 模拟汇总评审API调用，耗时: {mock_api_time}秒, 输入Token: {mock_input_tokens}, 输出Token: {mock_output_tokens}")

            return {
                "summary": f"[测试模式] 总体评审意见：\n共分析了 {len(review_results)} 个章节\n总体评审结论：基本符合要求\n主要问题：无重大问题\n改进建议：建议进一步完善细节"
            }

        # 提取关键信息用于汇总
        summary_data = {
            "total_sections": len(review_results),
            "sections_with_content": 0,
            "compliance_issues": [],
            "non_compliance_issues": [],
            "not_applicable_count": 0,
            "total_criteria": 0
        }

        prompt = f"""你是专业的可研报告评审专家，之前的任务已经使用大模型针对每个审查细则，给出了各章节的审查情况，请根据这些结果，生成一份针对整份报告的总体评审意见。

# 评审结果
{review_results}

# 输出格式

请按以下JSON格式给出总体评审意见：
```json
{{
  "overall_conclusion": "符合/基本符合/不符合",
  "compliance_rate": "合规率百分比",
  "major_issues": [
    "主要问题1",
    "主要问题2"
  ],
  "improvement_suggestions": [
    "改进建议1",
    "改进建议2"
  ],
  "summary_text": "总体评审意见的文字描述"
}}
```

注意：
1. 基于实际统计数据进行总结
2. 重点关注不符合项目
3. 建议要具体可操作
4. 输出必须是有效的JSON格式"""

        try:
            print(f"正在调用大模型汇总评审结果...")
            print(f"共有 {len(review_results)} 个章节的评审结果")
            print(f"汇总评审提示词：{prompt}")

            # 记录API调用开始时间
            api_start_time = time.time()

            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": "你是一个专业的可研报告评审专家，请严格按照JSON格式回答。"},
                    {"role": "user", "content": prompt}
                ],
                timeout=120  # 设置120秒超时
            )

            # 记录API调用结束时间
            api_end_time = time.time()
            api_duration = api_end_time - api_start_time

            # 更新统计信息
            self.stats["api_calls"] += 1
            self.stats["total_api_time"] += api_duration

            # 提取token使用信息
            if hasattr(response, 'usage') and response.usage:
                if hasattr(response.usage, 'prompt_tokens'):
                    self.stats["total_input_tokens"] += response.usage.prompt_tokens
                if hasattr(response.usage, 'completion_tokens'):
                    self.stats["total_output_tokens"] += response.usage.completion_tokens
                if hasattr(response.usage, 'total_tokens'):
                    self.stats["total_tokens"] += response.usage.total_tokens

                print(f"汇总评审API调用成功，耗时: {api_duration:.2f}秒, 输入Token: {response.usage.prompt_tokens}, 输出Token: {response.usage.completion_tokens}")
            else:
                print(f"汇总评审API调用成功，耗时: {api_duration:.2f}秒")

            # 清理响应内容
            raw_content = response.choices[0].message.content
            cleaned_content = self._clean_response(raw_content)

            print(f"\n汇总评审-大模型汇总响应: {cleaned_content}...\n")

            # 尝试解析JSON响应
            try:
                import json
                # 提取JSON部分
                json_start = cleaned_content.find('{')
                json_end = cleaned_content.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = cleaned_content[json_start:json_end]
                    result = json.loads(json_str)
                    return {"summary": result}
                else:
                    raise ValueError("未找到有效的JSON格式")
            except Exception as json_error:
                print(f"JSON解析失败: {json_error}")
                # 如果JSON解析失败，返回原始文本格式
                return {
                    "summary": {
                        "overall_conclusion": "不适用",
                        "compliance_rate": "0%",
                        "major_issues": ["JSON解析失败"],
                        "improvement_suggestions": ["请检查大模型响应格式"],
                        "summary_text": f"汇总失败，原始响应：{cleaned_content[:500]}"
                    }
                }

        except Exception as e:
            print(f"API调用失败: {e}")
            return {
                "summary": {
                    "overall_conclusion": "不适用",
                    "compliance_rate": "0%",
                    "major_issues": [f"API调用失败：{str(e)}"],
                    "improvement_suggestions": ["请检查网络连接和API配置"],
                    "summary_text": f"汇总失败：{str(e)}"
                }
            }